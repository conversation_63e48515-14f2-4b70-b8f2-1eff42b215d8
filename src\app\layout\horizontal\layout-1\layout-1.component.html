<ng-container>
    <ng-container *ngTemplateOutlet="leftNavbar"></ng-container>
</ng-container>
<div id="main">

    <!-- TOOLBAR: Above -->
    <ng-container *ngIf="fuseConfig.layout.toolbar.position === 'above'">
        <ng-container *ngTemplateOutlet="toolbar"></ng-container>
    </ng-container>

    <!-- NAVBAR: Top -->
    <ng-container>
        <ng-container *ngTemplateOutlet="topNavbar"></ng-container>
    </ng-container>

    <div id="container-1" class="container">

        <div id="container-2" class="container">

            <div id="container-3" class="container" fusePerfectScrollbar
                [fusePerfectScrollbarOptions]="{suppressScrollX: true, updateOnRouteChange : true}">

                <!-- CONTENT -->
                <content></content>

            </div>

        </div>

    </div>

</div>
<!-- TOOLBAR -->
<ng-template #toolbar>
    <toolbar *ngIf="!fuseConfig.layout.toolbar.hidden"
        [ngClass]="fuseConfig.layout.toolbar.customBackgroundColor === true ? fuseConfig.layout.toolbar.position + ' ' + fuseConfig.layout.toolbar.background : fuseConfig.layout.toolbar.position">
    </toolbar>
</ng-template>

<!-- TOP NAVBAR -->
<!-- <ng-template #topNavbar>
    <navbar variant="horizontal-style-1" class="top-navbar" fxHide fxShow.gt-md
        *ngIf="!fuseConfig.layout.navbar.hidden">
    </navbar>
</ng-template> -->

<!-- LEFT NAVBAR -->
<ng-template #leftNavbar>
    <fuse-sidebar name="navbar" class="navbar-fuse-sidebar" [folded]="fuseConfig.layout.navbar.folded"
        *ngIf="!fuseConfig.layout.navbar.hidden">
        <navbar variant="vertical-style-1" class="left-navbar"></navbar>
    </fuse-sidebar>
</ng-template>